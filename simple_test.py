#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("=== 开始简单测试 ===")

# 测试1: 基本Python功能
print("1. Python基本功能测试...")
try:
    import sys
    print(f"   Python版本: {sys.version}")
    print("   ✅ Python基本功能正常")
except Exception as e:
    print(f"   ❌ Python基本功能错误: {e}")

# 测试2: 导入标准库
print("\n2. 标准库导入测试...")
try:
    import os
    import datetime
    print("   ✅ 标准库导入正常")
except Exception as e:
    print(f"   ❌ 标准库导入错误: {e}")

# 测试3: 导入第三方库
print("\n3. 第三方库导入测试...")
libraries = ['pandas', 'numpy', 'sqlalchemy', 'pymysql', 'tushare']
for lib in libraries:
    try:
        __import__(lib)
        print(f"   ✅ {lib} - 导入成功")
    except ImportError as e:
        print(f"   ❌ {lib} - 导入失败: {e}")
    except Exception as e:
        print(f"   ⚠️  {lib} - 其他错误: {e}")

# 测试4: 导入本地模块
print("\n4. 本地模块导入测试...")
try:
    import config
    print(f"   ✅ config模块导入成功")
    print(f"   股票代码: {config.GREE_STOCK_CODE}")
except Exception as e:
    print(f"   ❌ config模块导入失败: {e}")

try:
    from models import create_tables
    print("   ✅ models模块导入成功")
except Exception as e:
    print(f"   ❌ models模块导入失败: {e}")

try:
    from data_fetcher import TushareDataFetcher
    print("   ✅ data_fetcher模块导入成功")
except Exception as e:
    print(f"   ❌ data_fetcher模块导入失败: {e}")

try:
    from data_storage import DataStorage
    print("   ✅ data_storage模块导入成功")
except Exception as e:
    print(f"   ❌ data_storage模块导入失败: {e}")

print("\n=== 简单测试完成 ===")
