# Tushare数据获取器
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
import config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TushareDataFetcher:
    """Tushare数据获取器"""
    
    def __init__(self):
        """初始化Tushare API"""
        ts.set_token(config.TUSHARE_TOKEN)
        self.pro = ts.pro_api()
        self.stock_code = config.GREE_STOCK_CODE
        
    def get_daily_data(self, start_date=None, end_date=None):
        """
        获取日线行情数据
        
        Args:
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            
        Returns:
            pd.DataFrame: 日线数据
        """
        try:
            logger.info(f"开始获取{self.stock_code}的日线数据...")
            
            # 如果没有指定日期，获取最近5年的数据
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=5*365)).strftime('%Y%m%d')
            
            # 获取日线数据
            df = self.pro.daily(
                ts_code=self.stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning("未获取到日线数据")
                return pd.DataFrame()
            
            # 数据预处理
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            
            logger.info(f"成功获取{len(df)}条日线数据")
            return df
            
        except Exception as e:
            logger.error(f"获取日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_income_statement(self, start_date=None, end_date=None):
        """
        获取利润表数据
        
        Args:
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            
        Returns:
            pd.DataFrame: 利润表数据
        """
        try:
            logger.info(f"开始获取{self.stock_code}的利润表数据...")
            
            # 如果没有指定日期，获取最近10年的数据
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=10*365)).strftime('%Y%m%d')
            
            # 获取利润表数据
            df = self.pro.income(
                ts_code=self.stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning("未获取到利润表数据")
                return pd.DataFrame()
            
            # 数据预处理
            df['end_date'] = pd.to_datetime(df['end_date'])
            if 'ann_date' in df.columns:
                df['ann_date'] = pd.to_datetime(df['ann_date'])
            if 'f_ann_date' in df.columns:
                df['f_ann_date'] = pd.to_datetime(df['f_ann_date'])
            
            df = df.sort_values('end_date')
            
            logger.info(f"成功获取{len(df)}条利润表数据")
            return df
            
        except Exception as e:
            logger.error(f"获取利润表数据失败: {e}")
            return pd.DataFrame()
    
    def get_balance_sheet(self, start_date=None, end_date=None):
        """
        获取资产负债表数据
        
        Args:
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            
        Returns:
            pd.DataFrame: 资产负债表数据
        """
        try:
            logger.info(f"开始获取{self.stock_code}的资产负债表数据...")
            
            # 如果没有指定日期，获取最近10年的数据
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=10*365)).strftime('%Y%m%d')
            
            # 获取资产负债表数据
            df = self.pro.balancesheet(
                ts_code=self.stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning("未获取到资产负债表数据")
                return pd.DataFrame()
            
            # 数据预处理
            df['end_date'] = pd.to_datetime(df['end_date'])
            if 'ann_date' in df.columns:
                df['ann_date'] = pd.to_datetime(df['ann_date'])
            if 'f_ann_date' in df.columns:
                df['f_ann_date'] = pd.to_datetime(df['f_ann_date'])
            
            df = df.sort_values('end_date')
            
            logger.info(f"成功获取{len(df)}条资产负债表数据")
            return df
            
        except Exception as e:
            logger.error(f"获取资产负债表数据失败: {e}")
            return pd.DataFrame()
    
    def get_cash_flow_statement(self, start_date=None, end_date=None):
        """
        获取现金流量表数据
        
        Args:
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            
        Returns:
            pd.DataFrame: 现金流量表数据
        """
        try:
            logger.info(f"开始获取{self.stock_code}的现金流量表数据...")
            
            # 如果没有指定日期，获取最近10年的数据
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=10*365)).strftime('%Y%m%d')
            
            # 获取现金流量表数据
            df = self.pro.cashflow(
                ts_code=self.stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning("未获取到现金流量表数据")
                return pd.DataFrame()
            
            # 数据预处理
            df['end_date'] = pd.to_datetime(df['end_date'])
            if 'ann_date' in df.columns:
                df['ann_date'] = pd.to_datetime(df['ann_date'])
            if 'f_ann_date' in df.columns:
                df['f_ann_date'] = pd.to_datetime(df['f_ann_date'])
            
            df = df.sort_values('end_date')
            
            logger.info(f"成功获取{len(df)}条现金流量表数据")
            return df
            
        except Exception as e:
            logger.error(f"获取现金流量表数据失败: {e}")
            return pd.DataFrame()
    
    def get_all_financial_data(self, start_date=None, end_date=None):
        """
        获取所有财务数据
        
        Args:
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            
        Returns:
            dict: 包含所有财务数据的字典
        """
        logger.info("开始获取所有财务数据...")
        
        data = {}
        
        # 获取日线数据
        data['daily'] = self.get_daily_data(start_date, end_date)
        time.sleep(0.5)  # API限制，避免请求过快
        
        # 获取利润表数据
        data['income'] = self.get_income_statement(start_date, end_date)
        time.sleep(0.5)
        
        # 获取资产负债表数据
        data['balance'] = self.get_balance_sheet(start_date, end_date)
        time.sleep(0.5)
        
        # 获取现金流量表数据
        data['cashflow'] = self.get_cash_flow_statement(start_date, end_date)
        
        logger.info("所有财务数据获取完成")
        return data
    
    def get_basic_info(self):
        """
        获取股票基本信息
        
        Returns:
            pd.DataFrame: 股票基本信息
        """
        try:
            logger.info(f"获取{self.stock_code}基本信息...")
            
            df = self.pro.stock_basic(
                ts_code=self.stock_code,
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            if not df.empty:
                logger.info(f"股票名称: {df.iloc[0]['name']}")
                logger.info(f"所属行业: {df.iloc[0]['industry']}")
                logger.info(f"上市日期: {df.iloc[0]['list_date']}")
            
            return df
            
        except Exception as e:
            logger.error(f"获取基本信息失败: {e}")
            return pd.DataFrame()

if __name__ == "__main__":
    # 测试数据获取器
    fetcher = TushareDataFetcher()
    
    # 获取基本信息
    basic_info = fetcher.get_basic_info()
    print("基本信息:")
    print(basic_info)
    
    # 获取最近1年的数据进行测试
    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
    end_date = datetime.now().strftime('%Y%m%d')
    
    # 测试获取日线数据
    daily_data = fetcher.get_daily_data(start_date, end_date)
    print(f"\n日线数据样本 (共{len(daily_data)}条):")
    print(daily_data.head())
    
    # 测试获取财务报表数据
    income_data = fetcher.get_income_statement()
    print(f"\n利润表数据样本 (共{len(income_data)}条):")
    print(income_data.head())
