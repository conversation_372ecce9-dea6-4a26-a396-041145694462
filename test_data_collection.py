# 数据收集测试和验证脚本
import unittest
import pandas as pd
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_fetcher import TushareDataFetcher
from data_storage import DataStorage
from models import create_tables, get_session, DailyStockData, FinancialStatements
import config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestDataCollection(unittest.TestCase):
    """数据收集测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        logger.info("开始数据收集测试...")
        cls.fetcher = TushareDataFetcher()
        cls.storage = DataStorage()
        
        # 创建测试数据库表
        create_tables()
    
    def test_01_tushare_connection(self):
        """测试Tushare连接"""
        logger.info("测试1: Tushare API连接...")
        
        # 获取基本信息
        basic_info = self.fetcher.get_basic_info()
        
        self.assertFalse(basic_info.empty, "应该能够获取到股票基本信息")
        self.assertEqual(basic_info.iloc[0]['ts_code'], config.GREE_STOCK_CODE, 
                        "股票代码应该匹配")
        
        logger.info(f"✅ Tushare连接正常，股票名称: {basic_info.iloc[0]['name']}")
    
    def test_02_fetch_daily_data(self):
        """测试获取日线数据"""
        logger.info("测试2: 获取日线数据...")
        
        # 获取最近30天的数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        daily_data = self.fetcher.get_daily_data(start_date, end_date)
        
        self.assertFalse(daily_data.empty, "应该能够获取到日线数据")
        self.assertIn('ts_code', daily_data.columns, "应该包含股票代码列")
        self.assertIn('trade_date', daily_data.columns, "应该包含交易日期列")
        self.assertIn('close', daily_data.columns, "应该包含收盘价列")
        
        # 验证数据类型
        self.assertTrue(pd.api.types.is_datetime64_any_dtype(daily_data['trade_date']), 
                       "交易日期应该是日期类型")
        
        logger.info(f"✅ 成功获取{len(daily_data)}条日线数据")
    
    def test_03_fetch_financial_data(self):
        """测试获取财务报表数据"""
        logger.info("测试3: 获取财务报表数据...")
        
        # 获取最近2年的财务数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=2*365)).strftime('%Y%m%d')
        
        # 测试利润表
        income_data = self.fetcher.get_income_statement(start_date, end_date)
        self.assertFalse(income_data.empty, "应该能够获取到利润表数据")
        self.assertIn('ts_code', income_data.columns, "利润表应该包含股票代码列")
        self.assertIn('end_date', income_data.columns, "利润表应该包含报告期列")
        
        # 测试资产负债表
        balance_data = self.fetcher.get_balance_sheet(start_date, end_date)
        self.assertFalse(balance_data.empty, "应该能够获取到资产负债表数据")
        self.assertIn('ts_code', balance_data.columns, "资产负债表应该包含股票代码列")
        
        # 测试现金流量表
        cashflow_data = self.fetcher.get_cash_flow_statement(start_date, end_date)
        self.assertFalse(cashflow_data.empty, "应该能够获取到现金流量表数据")
        self.assertIn('ts_code', cashflow_data.columns, "现金流量表应该包含股票代码列")
        
        logger.info(f"✅ 成功获取财务数据 - 利润表:{len(income_data)}条, "
                   f"资产负债表:{len(balance_data)}条, 现金流量表:{len(cashflow_data)}条")
    
    def test_04_data_storage(self):
        """测试数据存储"""
        logger.info("测试4: 数据存储...")
        
        # 获取少量测试数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        daily_data = self.fetcher.get_daily_data(start_date, end_date)
        
        if not daily_data.empty:
            # 测试保存日线数据
            saved_count = self.storage.save_daily_data(daily_data)
            self.assertGreater(saved_count, 0, "应该成功保存日线数据")
            
            # 验证数据是否真的保存到数据库
            session = get_session()
            try:
                db_count = session.query(DailyStockData).filter(
                    DailyStockData.ts_code == config.GREE_STOCK_CODE
                ).count()
                self.assertGreater(db_count, 0, "数据库中应该有日线数据")
            finally:
                session.close()
            
            logger.info(f"✅ 成功保存{saved_count}条日线数据到数据库")
        else:
            logger.warning("⚠️  没有获取到日线数据，跳过存储测试")
    
    def test_05_data_validation(self):
        """测试数据验证"""
        logger.info("测试5: 数据验证...")
        
        # 获取数据统计
        summary = self.storage.get_data_summary()
        
        self.assertIsInstance(summary, dict, "数据统计应该返回字典")
        self.assertIn('daily_count', summary, "应该包含日线数据统计")
        
        # 如果有数据，验证数据的完整性
        if summary.get('daily_count', 0) > 0:
            session = get_session()
            try:
                # 检查是否有空的关键字段
                null_close_count = session.query(DailyStockData).filter(
                    DailyStockData.close.is_(None)
                ).count()
                
                total_count = session.query(DailyStockData).count()
                null_ratio = null_close_count / total_count if total_count > 0 else 0
                
                self.assertLess(null_ratio, 0.1, "收盘价为空的记录应该少于10%")
                
                logger.info(f"✅ 数据验证通过 - 总记录数:{total_count}, "
                           f"收盘价为空:{null_close_count}, 空值比例:{null_ratio:.2%}")
            finally:
                session.close()
        else:
            logger.info("ℹ️  数据库中暂无数据，跳过数据验证")
    
    def test_06_error_handling(self):
        """测试错误处理"""
        logger.info("测试6: 错误处理...")
        
        # 测试无效日期范围
        invalid_data = self.fetcher.get_daily_data('20991231', '20991231')
        self.assertTrue(invalid_data.empty, "无效日期应该返回空数据")
        
        # 测试保存空数据
        empty_save_result = self.storage.save_daily_data(pd.DataFrame())
        self.assertEqual(empty_save_result, 0, "保存空数据应该返回0")
        
        logger.info("✅ 错误处理测试通过")

class DataValidationTools:
    """数据验证工具类"""
    
    def __init__(self):
        self.storage = DataStorage()
    
    def validate_data_integrity(self):
        """验证数据完整性"""
        logger.info("开始数据完整性验证...")
        
        session = get_session()
        try:
            results = {}
            
            # 验证日线数据
            daily_stats = self._validate_daily_data(session)
            results['daily'] = daily_stats
            
            # 验证财务数据
            financial_stats = self._validate_financial_data(session)
            results['financial'] = financial_stats
            
            # 生成验证报告
            self._generate_validation_report(results)
            
            return results
            
        finally:
            session.close()
    
    def _validate_daily_data(self, session):
        """验证日线数据"""
        stats = {}
        
        # 总记录数
        total_count = session.query(DailyStockData).count()
        stats['total_count'] = total_count
        
        if total_count > 0:
            # 检查关键字段的空值情况
            null_counts = {}
            for field in ['open', 'high', 'low', 'close', 'vol']:
                null_count = session.query(DailyStockData).filter(
                    getattr(DailyStockData, field).is_(None)
                ).count()
                null_counts[field] = null_count
            
            stats['null_counts'] = null_counts
            
            # 检查数据范围
            latest_date = session.query(DailyStockData).order_by(
                DailyStockData.trade_date.desc()
            ).first().trade_date
            
            earliest_date = session.query(DailyStockData).order_by(
                DailyStockData.trade_date.asc()
            ).first().trade_date
            
            stats['date_range'] = {
                'earliest': earliest_date,
                'latest': latest_date
            }
        
        return stats
    
    def _validate_financial_data(self, session):
        """验证财务数据"""
        stats = {}
        
        # 利润表数据统计
        income_count = session.query(FinancialStatements).count()
        stats['income_count'] = income_count
        
        if income_count > 0:
            latest_income = session.query(FinancialStatements).order_by(
                FinancialStatements.end_date.desc()
            ).first()
            stats['latest_income_date'] = latest_income.end_date
        
        return stats
    
    def _generate_validation_report(self, results):
        """生成验证报告"""
        logger.info("=" * 50)
        logger.info("数据验证报告")
        logger.info("=" * 50)
        
        # 日线数据报告
        daily_stats = results.get('daily', {})
        logger.info(f"日线数据总数: {daily_stats.get('total_count', 0)}")
        
        if 'null_counts' in daily_stats:
            logger.info("关键字段空值统计:")
            for field, count in daily_stats['null_counts'].items():
                ratio = count / daily_stats['total_count'] * 100
                logger.info(f"  {field}: {count} ({ratio:.1f}%)")
        
        if 'date_range' in daily_stats:
            logger.info(f"数据时间范围: {daily_stats['date_range']['earliest']} "
                       f"到 {daily_stats['date_range']['latest']}")
        
        # 财务数据报告
        financial_stats = results.get('financial', {})
        logger.info(f"利润表数据总数: {financial_stats.get('income_count', 0)}")
        
        if 'latest_income_date' in financial_stats:
            logger.info(f"最新财务数据日期: {financial_stats['latest_income_date']}")
        
        logger.info("=" * 50)

def run_tests():
    """运行所有测试"""
    logger.info("开始运行数据收集测试套件...")
    
    # 运行单元测试
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestDataCollection)
    test_runner = unittest.TextTestRunner(verbosity=2)
    test_result = test_runner.run(test_suite)
    
    # 运行数据验证
    validator = DataValidationTools()
    validation_results = validator.validate_data_integrity()
    
    return test_result.wasSuccessful(), validation_results

if __name__ == "__main__":
    success, validation_results = run_tests()
    
    if success:
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 部分测试失败!")
        sys.exit(1)
