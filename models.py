# 数据库模型定义
from sqlalchemy import create_engine, Column, String, Float, Date, DateTime, Integer, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import config

Base = declarative_base()

class DailyStockData(Base):
    """日线行情数据表"""
    __tablename__ = 'daily_stock_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    ts_code = Column(String(20), nullable=False, comment='股票代码')
    trade_date = Column(Date, nullable=False, comment='交易日期')
    open = Column(Float, comment='开盘价')
    high = Column(Float, comment='最高价')
    low = Column(Float, comment='最低价')
    close = Column(Float, comment='收盘价')
    pre_close = Column(Float, comment='昨收价')
    change = Column(Float, comment='涨跌额')
    pct_chg = Column(Float, comment='涨跌幅')
    vol = Column(Float, comment='成交量(手)')
    amount = Column(Float, comment='成交额(千元)')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 创建复合索引
    __table_args__ = (
        Index('idx_ts_code_trade_date', 'ts_code', 'trade_date'),
        Index('idx_trade_date', 'trade_date'),
    )

class FinancialStatements(Base):
    """财务报表数据表"""
    __tablename__ = 'financial_statements'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    ts_code = Column(String(20), nullable=False, comment='股票代码')
    ann_date = Column(Date, comment='公告日期')
    f_ann_date = Column(Date, comment='实际公告日期')
    end_date = Column(Date, nullable=False, comment='报告期')
    report_type = Column(String(20), comment='报告类型')
    comp_type = Column(String(20), comment='公司类型')
    
    # 利润表主要指标
    total_revenue = Column(Float, comment='营业总收入')
    revenue = Column(Float, comment='营业收入')
    int_income = Column(Float, comment='利息收入')
    prem_earned = Column(Float, comment='已赚保费')
    comm_income = Column(Float, comment='手续费及佣金收入')
    n_commis_income = Column(Float, comment='手续费及佣金净收入')
    n_oth_income = Column(Float, comment='其他经营净收益')
    n_oth_b_income = Column(Float, comment='加:其他业务净收益')
    prem_income = Column(Float, comment='保险业务收入')
    out_prem = Column(Float, comment='减:分出保费')
    une_prem_reser = Column(Float, comment='提取未到期责任准备金')
    reins_income = Column(Float, comment='其中:分保费收入')
    n_sec_tb_income = Column(Float, comment='代理买卖证券业务净收入')
    n_sec_uw_income = Column(Float, comment='证券承销业务净收入')
    n_asset_mg_income = Column(Float, comment='受托客户资产管理业务净收入')
    oth_b_income = Column(Float, comment='其他业务收入')
    fv_value_chg_gain = Column(Float, comment='加:公允价值变动净收益')
    invest_income = Column(Float, comment='加:投资净收益')
    ass_invest_income = Column(Float, comment='其中:对联营企业和合营企业的投资收益')
    forex_gain = Column(Float, comment='加:汇兑净收益')
    total_cogs = Column(Float, comment='营业总成本')
    oper_cost = Column(Float, comment='减:营业成本')
    int_exp = Column(Float, comment='减:利息支出')
    comm_exp = Column(Float, comment='减:手续费及佣金支出')
    biz_tax_surchg = Column(Float, comment='减:营业税金及附加')
    sell_exp = Column(Float, comment='减:销售费用')
    admin_exp = Column(Float, comment='减:管理费用')
    fin_exp = Column(Float, comment='减:财务费用')
    assets_impair_loss = Column(Float, comment='减:资产减值损失')
    prem_refund = Column(Float, comment='退保金')
    compens_payout = Column(Float, comment='赔付总支出')
    reser_insur_liab = Column(Float, comment='提取保险责任准备金')
    div_payt = Column(Float, comment='保户红利支出')
    reins_exp = Column(Float, comment='分保费用')
    oper_exp = Column(Float, comment='营业支出')
    compens_payout_refu = Column(Float, comment='减:摊回赔付支出')
    insur_reser_refu = Column(Float, comment='减:摊回保险责任准备金')
    reins_cost_refund = Column(Float, comment='减:摊回分保费用')
    other_bus_cost = Column(Float, comment='其他业务成本')
    operate_profit = Column(Float, comment='营业利润')
    non_oper_income = Column(Float, comment='加:营业外收入')
    non_oper_exp = Column(Float, comment='减:营业外支出')
    nca_disploss = Column(Float, comment='其中:减:非流动资产处置净损失')
    total_profit = Column(Float, comment='利润总额')
    income_tax = Column(Float, comment='减:所得税费用')
    n_income = Column(Float, comment='净利润(含少数股东损益)')
    n_income_attr_p = Column(Float, comment='净利润(不含少数股东损益)')
    minority_gain = Column(Float, comment='少数股东损益')
    oth_compr_income = Column(Float, comment='其他综合收益')
    t_compr_income = Column(Float, comment='综合收益总额')
    compr_inc_attr_p = Column(Float, comment='归属于母公司(或股东)的综合收益总额')
    compr_inc_attr_m_s = Column(Float, comment='归属于少数股东的综合收益总额')
    ebit = Column(Float, comment='息税前利润')
    ebitda = Column(Float, comment='息税折旧摊销前利润')
    insurance_exp = Column(Float, comment='保险业务支出')
    undist_profit = Column(Float, comment='年初未分配利润')
    distable_profit = Column(Float, comment='可分配利润')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 创建复合索引
    __table_args__ = (
        Index('idx_ts_code_end_date', 'ts_code', 'end_date'),
        Index('idx_end_date', 'end_date'),
        Index('idx_ann_date', 'ann_date'),
    )

class BalanceSheet(Base):
    """资产负债表数据"""
    __tablename__ = 'balance_sheet'

    id = Column(Integer, primary_key=True, autoincrement=True)
    ts_code = Column(String(20), nullable=False, comment='股票代码')
    ann_date = Column(Date, comment='公告日期')
    f_ann_date = Column(Date, comment='实际公告日期')
    end_date = Column(Date, nullable=False, comment='报告期')
    report_type = Column(String(20), comment='报告类型')
    comp_type = Column(String(20), comment='公司类型')

    # 资产
    total_share = Column(Float, comment='期末总股本')
    cap_rese = Column(Float, comment='资本公积金')
    undistr_porfit = Column(Float, comment='未分配利润')
    surplus_rese = Column(Float, comment='盈余公积金')
    special_rese = Column(Float, comment='专项储备')
    money_cap = Column(Float, comment='货币资金')
    trad_asset = Column(Float, comment='交易性金融资产')
    notes_receiv = Column(Float, comment='应收票据')
    accounts_receiv = Column(Float, comment='应收账款')
    oth_receiv = Column(Float, comment='其他应收款')
    prepayment = Column(Float, comment='预付款项')
    div_receiv = Column(Float, comment='应收股利')
    int_receiv = Column(Float, comment='应收利息')
    inventories = Column(Float, comment='存货')
    amor_exp = Column(Float, comment='长期待摊费用')
    nca_within_1y = Column(Float, comment='一年内到期的非流动资产')
    sett_rsrv = Column(Float, comment='结算备付金')
    loanto_oth_bank_fi = Column(Float, comment='拆出资金')
    premium_receiv = Column(Float, comment='应收保费')
    reinsur_receiv = Column(Float, comment='应收分保账款')
    reinsur_res_receiv = Column(Float, comment='应收分保合同准备金')
    pur_resale_fa = Column(Float, comment='买入返售金融资产')
    oth_cur_assets = Column(Float, comment='其他流动资产')
    total_cur_assets = Column(Float, comment='流动资产合计')
    fa_avail_for_sale = Column(Float, comment='可供出售金融资产')
    htm_invest = Column(Float, comment='持有至到期投资')
    lt_eqt_invest = Column(Float, comment='长期股权投资')
    invest_real_estate = Column(Float, comment='投资性房地产')
    time_deposits = Column(Float, comment='定期存款')
    oth_assets = Column(Float, comment='其他资产')
    lt_rec = Column(Float, comment='长期应收款')
    fix_assets = Column(Float, comment='固定资产')
    cip = Column(Float, comment='在建工程')
    const_materials = Column(Float, comment='工程物资')
    fixed_assets_disp = Column(Float, comment='固定资产清理')
    produc_bio_assets = Column(Float, comment='生产性生物资产')
    oil_and_gas_assets = Column(Float, comment='油气资产')
    intan_assets = Column(Float, comment='无形资产')
    r_and_d = Column(Float, comment='开发支出')
    goodwill = Column(Float, comment='商誉')
    lt_amor_exp = Column(Float, comment='长期待摊费用')
    defer_tax_assets = Column(Float, comment='递延所得税资产')
    decr_in_disbur = Column(Float, comment='发放贷款及垫款')
    oth_nca = Column(Float, comment='其他非流动资产')
    total_nca = Column(Float, comment='非流动资产合计')
    cash_reser_cb = Column(Float, comment='现金及存放中央银行款项')
    depos_in_oth_bfi = Column(Float, comment='存放同业和其它金融机构款项')
    prec_metals = Column(Float, comment='贵金属')
    deriv_assets = Column(Float, comment='衍生金融资产')
    rr_reins_une_prem = Column(Float, comment='应收分保未到期责任准备金')
    rr_reins_outstd_cla = Column(Float, comment='应收分保未决赔款准备金')
    rr_reins_lins_liab = Column(Float, comment='应收分保寿险责任准备金')
    rr_reins_lthins_liab = Column(Float, comment='应收分保长期健康险责任准备金')
    refund_depos = Column(Float, comment='存出保证金')
    ph_pledge_loans = Column(Float, comment='保户质押贷款')
    refund_cap_depos = Column(Float, comment='存出资本保证金')
    indep_acct_assets = Column(Float, comment='独立账户资产')
    client_depos = Column(Float, comment='其中：客户资金存款')
    client_prov = Column(Float, comment='其中：客户备付金')
    transac_seat_fee = Column(Float, comment='其中:交易席位费')
    invest_as_receiv = Column(Float, comment='应收款项类投资')
    total_assets = Column(Float, comment='资产总计')

    # 负债
    lt_borr = Column(Float, comment='长期借款')
    st_borr = Column(Float, comment='短期借款')
    cb_borr = Column(Float, comment='向中央银行借款')
    depos_ib_deposits = Column(Float, comment='吸收存款及同业存放')
    loan_oth_bank = Column(Float, comment='拆入资金')
    trading_fl = Column(Float, comment='交易性金融负债')
    notes_payable = Column(Float, comment='应付票据')
    acct_payable = Column(Float, comment='应付账款')
    adv_receipts = Column(Float, comment='预收款项')
    sold_for_repur_fa = Column(Float, comment='卖出回购金融资产款')
    comm_payable = Column(Float, comment='应付手续费及佣金')
    payroll_payable = Column(Float, comment='应付职工薪酬')
    taxes_payable = Column(Float, comment='应交税费')
    int_payable = Column(Float, comment='应付利息')
    div_payable = Column(Float, comment='应付股利')
    oth_payable = Column(Float, comment='其他应付款')
    acc_exp = Column(Float, comment='预提费用')
    deferred_inc = Column(Float, comment='递延收益')
    st_bonds_payable = Column(Float, comment='应付短期债券')
    payable_to_reinsurer = Column(Float, comment='应付分保账款')
    rsrv_insur_cont = Column(Float, comment='保险合同准备金')
    acting_trading_sec = Column(Float, comment='代理买卖证券款')
    acting_uw_sec = Column(Float, comment='代理承销证券款')
    non_cur_liab_due_1y = Column(Float, comment='一年内到期的非流动负债')
    oth_cur_liab = Column(Float, comment='其他流动负债')
    total_cur_liab = Column(Float, comment='流动负债合计')
    bond_payable = Column(Float, comment='应付债券')
    lt_payable = Column(Float, comment='长期应付款')
    specific_payables = Column(Float, comment='专项应付款')
    estimated_liab = Column(Float, comment='预计负债')
    defer_tax_liab = Column(Float, comment='递延所得税负债')
    defer_inc_non_cur_liab = Column(Float, comment='递延收益-非流动负债')
    oth_ncl = Column(Float, comment='其他非流动负债')
    total_ncl = Column(Float, comment='非流动负债合计')
    depos_oth_bfi = Column(Float, comment='同业和其它金融机构存放款项')
    deriv_liab = Column(Float, comment='衍生金融负债')
    depos = Column(Float, comment='吸收存款')
    agency_bus_liab = Column(Float, comment='代理业务负债')
    oth_liab = Column(Float, comment='其他负债')
    prem_receiv_adva = Column(Float, comment='预收保费')
    depos_received = Column(Float, comment='存入保证金')
    ph_invest = Column(Float, comment='保户储金及投资款')
    reser_une_prem = Column(Float, comment='未到期责任准备金')
    reser_outstd_claims = Column(Float, comment='未决赔款准备金')
    reser_lins_liab = Column(Float, comment='寿险责任准备金')
    reser_lthins_liab = Column(Float, comment='长期健康险责任准备金')
    indept_acc_liab = Column(Float, comment='独立账户负债')
    pledge_borr = Column(Float, comment='其中:质押借款')
    indem_payable = Column(Float, comment='应付赔付款')
    policy_div_payable = Column(Float, comment='应付保单红利')
    total_liab = Column(Float, comment='负债合计')

    # 所有者权益
    paid_in_capital = Column(Float, comment='实收资本(或股本)')
    oth_equity_tools = Column(Float, comment='其他权益工具')
    oth_equity_tools_p_shr = Column(Float, comment='其他权益工具:优先股')
    oth_equity_tools_p_bond = Column(Float, comment='其他权益工具:永续债')
    oth_equity_tools_oth = Column(Float, comment='其他权益工具:其他')
    treasury_share = Column(Float, comment='减:库存股')
    ordin_risk_reser = Column(Float, comment='一般风险准备')
    forex_differ = Column(Float, comment='外币报表折算差额')
    invest_loss_unconf = Column(Float, comment='未确认的投资损失')
    minority_int = Column(Float, comment='少数股东权益')
    total_hldr_eqy_exc_min_int = Column(Float, comment='股东权益合计(不含少数股东权益)')
    total_hldr_eqy_inc_min_int = Column(Float, comment='股东权益合计(含少数股东权益)')
    total_liab_hldr_eqy = Column(Float, comment='负债及股东权益总计')
    lt_payroll_payable = Column(Float, comment='长期应付职工薪酬')
    oth_comp_income = Column(Float, comment='其他综合收益')
    oth_eqt_ppbond = Column(Float, comment='其他权益工具:永续债')
    oth_eqt_othbond = Column(Float, comment='其他权益工具:其他')

    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 创建复合索引
    __table_args__ = (
        Index('idx_bs_ts_code_end_date', 'ts_code', 'end_date'),
        Index('idx_bs_end_date', 'end_date'),
        Index('idx_bs_ann_date', 'ann_date'),
    )

class CashFlowStatement(Base):
    """现金流量表数据"""
    __tablename__ = 'cash_flow_statement'

    id = Column(Integer, primary_key=True, autoincrement=True)
    ts_code = Column(String(20), nullable=False, comment='股票代码')
    ann_date = Column(Date, comment='公告日期')
    f_ann_date = Column(Date, comment='实际公告日期')
    end_date = Column(Date, nullable=False, comment='报告期')
    comp_type = Column(String(20), comment='公司类型')
    report_type = Column(String(20), comment='报告类型')

    # 经营活动现金流量
    net_profit = Column(Float, comment='净利润')
    finan_exp = Column(Float, comment='财务费用')
    c_fr_sale_sg = Column(Float, comment='销售商品、提供劳务收到的现金')
    recp_tax_rends = Column(Float, comment='收到的税费返还')
    n_depos_incr_fi = Column(Float, comment='客户存款和同业存放款项净增加额')
    n_incr_loans_cb = Column(Float, comment='向央行借款净增加额')
    n_inc_borr_oth_fi = Column(Float, comment='向其他金融机构拆入资金净增加额')
    prem_fr_orig_contr = Column(Float, comment='收到原保险合同保费取得的现金')
    n_incr_insured_dep = Column(Float, comment='保户储金净增加额')
    n_reinsur_prem = Column(Float, comment='收到再保业务现金净额')
    n_incr_disp_tfa = Column(Float, comment='处置交易性金融资产净增加额')
    ifc_cash_incr = Column(Float, comment='收取利息和手续费净增加额')
    n_incr_disp_faas = Column(Float, comment='处置可供出售金融资产净增加额')
    n_incr_loans_oth_bank = Column(Float, comment='拆入资金净增加额')
    n_cap_incr_repur = Column(Float, comment='回购业务资金净增加额')
    c_fr_oth_operate_a = Column(Float, comment='收到其他与经营活动有关的现金')
    c_inf_fr_operate_a = Column(Float, comment='经营活动现金流入小计')
    c_paid_goods_s = Column(Float, comment='购买商品、接受劳务支付的现金')
    c_paid_to_for_empl = Column(Float, comment='支付给职工以及为职工支付的现金')
    c_paid_for_taxes = Column(Float, comment='支付的各项税费')
    n_incr_clt_loan_adv = Column(Float, comment='客户贷款及垫款净增加额')
    n_incr_dep_cbob = Column(Float, comment='存放央行和同业款项净增加额')
    c_pay_claims_orig_inco = Column(Float, comment='支付原保险合同赔付款项的现金')
    pay_handling_chrg = Column(Float, comment='支付手续费的现金')
    pay_comm_insur_plcy = Column(Float, comment='支付保单红利的现金')
    c_paid_oth_operate_a = Column(Float, comment='支付其他与经营活动有关的现金')
    c_outf_fr_operate_a = Column(Float, comment='经营活动现金流出小计')
    net_cash_flows_oper_act = Column(Float, comment='经营活动产生的现金流量净额')

    # 投资活动现金流量
    c_recp_return_invest = Column(Float, comment='收回投资收到的现金')
    c_recp_invest_income = Column(Float, comment='取得投资收益收到的现金')
    n_recp_disp_fiolta = Column(Float, comment='处置固定资产无形资产和其他长期资产收回的现金净额')
    n_recp_disp_sobu = Column(Float, comment='处置子公司及其他营业单位收到的现金净额')
    c_recp_oth_invest_a = Column(Float, comment='收到其他与投资活动有关的现金')
    c_inf_fr_invest_a = Column(Float, comment='投资活动现金流入小计')
    c_paid_acq_const_fiolta = Column(Float, comment='购建固定资产无形资产和其他长期资产支付的现金')
    c_paid_invest = Column(Float, comment='投资支付的现金')
    n_disp_subs_oth_biz = Column(Float, comment='取得子公司及其他营业单位支付的现金净额')
    c_paid_oth_invest_a = Column(Float, comment='支付其他与投资活动有关的现金')
    c_outf_fr_invest_a = Column(Float, comment='投资活动现金流出小计')
    net_cash_flows_inv_act = Column(Float, comment='投资活动产生的现金流量净额')

    # 筹资活动现金流量
    c_recp_borrow = Column(Float, comment='取得借款收到的现金')
    proc_issue_bonds = Column(Float, comment='发行债券收到的现金')
    c_recp_oth_fin_a = Column(Float, comment='收到其他与筹资活动有关的现金')
    c_inf_fr_fin_a = Column(Float, comment='筹资活动现金流入小计')
    c_prepay_amt_borr = Column(Float, comment='偿还债务支付的现金')
    c_pay_dist_dpcp_int_exp = Column(Float, comment='分配股利利润或偿付利息支付的现金')
    incl_dvd_profit_paid_sc_ms = Column(Float, comment='其中:子公司支付给少数股东的股利利润')
    c_paid_oth_fin_a = Column(Float, comment='支付其他与筹资活动有关的现金')
    c_outf_fr_fin_a = Column(Float, comment='筹资活动现金流出小计')
    net_cash_flows_fin_act = Column(Float, comment='筹资活动产生的现金流量净额')

    # 汇率变动对现金的影响
    eff_fx_flu_cash = Column(Float, comment='汇率变动对现金的影响')

    # 现金及现金等价物净增加额
    n_incr_cash_cash_equ = Column(Float, comment='现金及现金等价物净增加额')
    c_cash_equ_beg_period = Column(Float, comment='期初现金及现金等价物余额')
    c_cash_equ_end_period = Column(Float, comment='期末现金及现金等价物余额')

    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 创建复合索引
    __table_args__ = (
        Index('idx_cf_ts_code_end_date', 'ts_code', 'end_date'),
        Index('idx_cf_end_date', 'end_date'),
        Index('idx_cf_ann_date', 'ann_date'),
    )

# 数据库连接和会话管理
def get_engine():
    """获取数据库引擎"""
    return create_engine(config.DATABASE_URL, echo=False)

def get_session():
    """获取数据库会话"""
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    return Session()

def create_tables():
    """创建所有表"""
    engine = get_engine()
    Base.metadata.create_all(engine)
    print("数据库表创建完成")

if __name__ == "__main__":
    create_tables()
