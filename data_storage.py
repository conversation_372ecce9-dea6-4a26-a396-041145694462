# 数据存储模块
import pandas as pd
import numpy as np
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_
import logging
from datetime import datetime
from models import (
    DailyStockData, FinancialStatements, BalanceSheet, CashFlowStatement,
    get_session, create_tables
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataStorage:
    """数据存储管理器"""
    
    def __init__(self):
        """初始化数据存储器"""
        self.session = get_session()
        
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'session'):
            self.session.close()
    
    def clean_dataframe(self, df):
        """
        清理DataFrame数据
        
        Args:
            df (pd.DataFrame): 原始数据
            
        Returns:
            pd.DataFrame: 清理后的数据
        """
        if df.empty:
            return df
        
        # 替换无穷大和NaN值
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # 将NaN转换为None（数据库NULL）
        df = df.where(pd.notnull(df), None)
        
        return df
    
    def save_daily_data(self, df):
        """
        保存日线数据到数据库
        
        Args:
            df (pd.DataFrame): 日线数据
            
        Returns:
            int: 成功保存的记录数
        """
        if df.empty:
            logger.warning("日线数据为空，跳过保存")
            return 0
        
        try:
            logger.info("开始保存日线数据...")
            df = self.clean_dataframe(df)
            
            saved_count = 0
            for _, row in df.iterrows():
                # 检查是否已存在
                existing = self.session.query(DailyStockData).filter(
                    and_(
                        DailyStockData.ts_code == row['ts_code'],
                        DailyStockData.trade_date == row['trade_date']
                    )
                ).first()
                
                if existing:
                    # 更新现有记录
                    for column in df.columns:
                        if hasattr(existing, column):
                            setattr(existing, column, row[column])
                    existing.updated_at = datetime.now()
                else:
                    # 创建新记录
                    record = DailyStockData(
                        ts_code=row['ts_code'],
                        trade_date=row['trade_date'],
                        open=row.get('open'),
                        high=row.get('high'),
                        low=row.get('low'),
                        close=row.get('close'),
                        pre_close=row.get('pre_close'),
                        change=row.get('change'),
                        pct_chg=row.get('pct_chg'),
                        vol=row.get('vol'),
                        amount=row.get('amount')
                    )
                    self.session.add(record)
                
                saved_count += 1
            
            self.session.commit()
            logger.info(f"成功保存{saved_count}条日线数据")
            return saved_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"保存日线数据失败: {e}")
            return 0
    
    def save_income_data(self, df):
        """
        保存利润表数据到数据库
        
        Args:
            df (pd.DataFrame): 利润表数据
            
        Returns:
            int: 成功保存的记录数
        """
        if df.empty:
            logger.warning("利润表数据为空，跳过保存")
            return 0
        
        try:
            logger.info("开始保存利润表数据...")
            df = self.clean_dataframe(df)
            
            saved_count = 0
            for _, row in df.iterrows():
                # 检查是否已存在
                existing = self.session.query(FinancialStatements).filter(
                    and_(
                        FinancialStatements.ts_code == row['ts_code'],
                        FinancialStatements.end_date == row['end_date']
                    )
                ).first()
                
                if existing:
                    # 更新现有记录
                    for column in df.columns:
                        if hasattr(existing, column):
                            setattr(existing, column, row[column])
                    existing.updated_at = datetime.now()
                else:
                    # 创建新记录，只包含模型中定义的字段
                    record_data = {}
                    for column in df.columns:
                        if hasattr(FinancialStatements, column):
                            record_data[column] = row[column]
                    
                    record = FinancialStatements(**record_data)
                    self.session.add(record)
                
                saved_count += 1
            
            self.session.commit()
            logger.info(f"成功保存{saved_count}条利润表数据")
            return saved_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"保存利润表数据失败: {e}")
            return 0
    
    def save_balance_sheet_data(self, df):
        """
        保存资产负债表数据到数据库
        
        Args:
            df (pd.DataFrame): 资产负债表数据
            
        Returns:
            int: 成功保存的记录数
        """
        if df.empty:
            logger.warning("资产负债表数据为空，跳过保存")
            return 0
        
        try:
            logger.info("开始保存资产负债表数据...")
            df = self.clean_dataframe(df)
            
            saved_count = 0
            for _, row in df.iterrows():
                # 检查是否已存在
                existing = self.session.query(BalanceSheet).filter(
                    and_(
                        BalanceSheet.ts_code == row['ts_code'],
                        BalanceSheet.end_date == row['end_date']
                    )
                ).first()
                
                if existing:
                    # 更新现有记录
                    for column in df.columns:
                        if hasattr(existing, column):
                            setattr(existing, column, row[column])
                    existing.updated_at = datetime.now()
                else:
                    # 创建新记录，只包含模型中定义的字段
                    record_data = {}
                    for column in df.columns:
                        if hasattr(BalanceSheet, column):
                            record_data[column] = row[column]
                    
                    record = BalanceSheet(**record_data)
                    self.session.add(record)
                
                saved_count += 1
            
            self.session.commit()
            logger.info(f"成功保存{saved_count}条资产负债表数据")
            return saved_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"保存资产负债表数据失败: {e}")
            return 0
    
    def save_cash_flow_data(self, df):
        """
        保存现金流量表数据到数据库
        
        Args:
            df (pd.DataFrame): 现金流量表数据
            
        Returns:
            int: 成功保存的记录数
        """
        if df.empty:
            logger.warning("现金流量表数据为空，跳过保存")
            return 0
        
        try:
            logger.info("开始保存现金流量表数据...")
            df = self.clean_dataframe(df)
            
            saved_count = 0
            for _, row in df.iterrows():
                # 检查是否已存在
                existing = self.session.query(CashFlowStatement).filter(
                    and_(
                        CashFlowStatement.ts_code == row['ts_code'],
                        CashFlowStatement.end_date == row['end_date']
                    )
                ).first()
                
                if existing:
                    # 更新现有记录
                    for column in df.columns:
                        if hasattr(existing, column):
                            setattr(existing, column, row[column])
                    existing.updated_at = datetime.now()
                else:
                    # 创建新记录，只包含模型中定义的字段
                    record_data = {}
                    for column in df.columns:
                        if hasattr(CashFlowStatement, column):
                            record_data[column] = row[column]
                    
                    record = CashFlowStatement(**record_data)
                    self.session.add(record)
                
                saved_count += 1
            
            self.session.commit()
            logger.info(f"成功保存{saved_count}条现金流量表数据")
            return saved_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"保存现金流量表数据失败: {e}")
            return 0
    
    def save_all_data(self, data_dict):
        """
        保存所有财务数据
        
        Args:
            data_dict (dict): 包含所有财务数据的字典
            
        Returns:
            dict: 保存结果统计
        """
        logger.info("开始保存所有财务数据...")
        
        results = {}
        
        # 保存日线数据
        if 'daily' in data_dict:
            results['daily'] = self.save_daily_data(data_dict['daily'])
        
        # 保存利润表数据
        if 'income' in data_dict:
            results['income'] = self.save_income_data(data_dict['income'])
        
        # 保存资产负债表数据
        if 'balance' in data_dict:
            results['balance'] = self.save_balance_sheet_data(data_dict['balance'])
        
        # 保存现金流量表数据
        if 'cashflow' in data_dict:
            results['cashflow'] = self.save_cash_flow_data(data_dict['cashflow'])
        
        logger.info("所有财务数据保存完成")
        logger.info(f"保存结果: {results}")
        
        return results
    
    def get_data_summary(self):
        """
        获取数据库中的数据统计
        
        Returns:
            dict: 数据统计信息
        """
        try:
            summary = {}
            
            # 日线数据统计
            daily_count = self.session.query(DailyStockData).count()
            summary['daily_count'] = daily_count
            
            if daily_count > 0:
                latest_daily = self.session.query(DailyStockData).order_by(
                    DailyStockData.trade_date.desc()
                ).first()
                summary['latest_daily_date'] = latest_daily.trade_date
            
            # 利润表数据统计
            income_count = self.session.query(FinancialStatements).count()
            summary['income_count'] = income_count
            
            if income_count > 0:
                latest_income = self.session.query(FinancialStatements).order_by(
                    FinancialStatements.end_date.desc()
                ).first()
                summary['latest_income_date'] = latest_income.end_date
            
            # 资产负债表数据统计
            balance_count = self.session.query(BalanceSheet).count()
            summary['balance_count'] = balance_count
            
            if balance_count > 0:
                latest_balance = self.session.query(BalanceSheet).order_by(
                    BalanceSheet.end_date.desc()
                ).first()
                summary['latest_balance_date'] = latest_balance.end_date
            
            # 现金流量表数据统计
            cashflow_count = self.session.query(CashFlowStatement).count()
            summary['cashflow_count'] = cashflow_count
            
            if cashflow_count > 0:
                latest_cashflow = self.session.query(CashFlowStatement).order_by(
                    CashFlowStatement.end_date.desc()
                ).first()
                summary['latest_cashflow_date'] = latest_cashflow.end_date
            
            return summary
            
        except Exception as e:
            logger.error(f"获取数据统计失败: {e}")
            return {}

if __name__ == "__main__":
    # 测试数据存储
    storage = DataStorage()
    
    # 创建数据库表
    create_tables()
    
    # 获取数据统计
    summary = storage.get_data_summary()
    print("数据库统计信息:")
    for key, value in summary.items():
        print(f"{key}: {value}")
