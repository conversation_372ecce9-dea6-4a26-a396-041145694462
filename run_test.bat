@echo off
echo Starting diagnostic tests...
echo.

echo 1. Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found or not working
    pause
    exit /b 1
)

echo.
echo 2. Installing required packages...
pip install tushare pandas sqlalchemy pymysql numpy python-dateutil
if %errorlevel% neq 0 (
    echo WARNING: Some packages may have failed to install
)

echo.
echo 3. Running simple test...
python simple_test.py
if %errorlevel% neq 0 (
    echo ERROR: Simple test failed
)

echo.
echo 4. Running quick test...
python quick_test.py
if %errorlevel% neq 0 (
    echo ERROR: Quick test failed
)

echo.
echo Tests completed. Press any key to continue...
pause
