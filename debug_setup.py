# 调试和设置检查脚本
import sys
import os

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = [
        'tushare',
        'pandas', 
        'sqlalchemy',
        'pymysql',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装的包: {', '.join(missing_packages)}")
        print("运行命令: pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n✅ 所有依赖包都已安装!")
        return True

def check_config():
    """检查配置文件"""
    print("\n检查配置文件...")
    
    try:
        import config
        
        # 检查Tushare Token
        if hasattr(config, 'TUSHARE_TOKEN') and config.TUSHARE_TOKEN:
            print(f"✅ Tushare Token: {config.TUSHARE_TOKEN[:10]}...")
        else:
            print("❌ Tushare Token 未配置")
            return False
        
        # 检查数据库配置
        if hasattr(config, 'DB_CONFIG') and config.DB_CONFIG:
            print(f"✅ 数据库配置: {config.DB_CONFIG['host']}:{config.DB_CONFIG['port']}")
        else:
            print("❌ 数据库配置未找到")
            return False
        
        # 检查股票代码
        if hasattr(config, 'GREE_STOCK_CODE') and config.GREE_STOCK_CODE:
            print(f"✅ 股票代码: {config.GREE_STOCK_CODE}")
        else:
            print("❌ 股票代码未配置")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入配置文件: {e}")
        return False

def test_tushare_connection():
    """测试Tushare连接"""
    print("\n测试Tushare连接...")
    
    try:
        import tushare as ts
        import config
        
        # 设置token
        ts.set_token(config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        # 测试获取基本信息
        df = pro.stock_basic(ts_code=config.GREE_STOCK_CODE, 
                           fields='ts_code,symbol,name,area,industry')
        
        if not df.empty:
            info = df.iloc[0]
            print(f"✅ Tushare连接成功!")
            print(f"   股票名称: {info['name']}")
            print(f"   所属行业: {info['industry']}")
            return True
        else:
            print("❌ 未获取到股票信息")
            return False
            
    except Exception as e:
        print(f"❌ Tushare连接失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n测试数据库连接...")
    
    try:
        from sqlalchemy import create_engine
        import config
        
        # 创建数据库引擎
        engine = create_engine(config.DATABASE_URL)
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            if result.fetchone():
                print("✅ 数据库连接成功!")
                return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查:")
        print("1. MySQL服务是否运行")
        print("2. 数据库配置是否正确")
        print("3. 数据库是否存在")
        return False

def create_database_if_not_exists():
    """如果数据库不存在则创建"""
    print("\n检查并创建数据库...")
    
    try:
        import pymysql
        import config
        
        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=config.DB_CONFIG['host'],
            port=config.DB_CONFIG['port'],
            user=config.DB_CONFIG['user'],
            password=config.DB_CONFIG['password']
        )
        
        with connection.cursor() as cursor:
            # 创建数据库（如果不存在）
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {config.DB_CONFIG['database']}")
            print(f"✅ 数据库 {config.DB_CONFIG['database']} 已确保存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("格力电器数据收集系统 - 环境检查")
    print("=" * 60)
    
    all_ok = True
    
    # 1. 检查依赖包
    if not check_dependencies():
        all_ok = False
    
    # 2. 检查配置
    if not check_config():
        all_ok = False
    
    # 如果基础检查通过，继续测试连接
    if all_ok:
        # 3. 创建数据库
        if not create_database_if_not_exists():
            all_ok = False
        
        # 4. 测试数据库连接
        if not test_database_connection():
            all_ok = False
        
        # 5. 测试Tushare连接
        if not test_tushare_connection():
            all_ok = False
    
    print("\n" + "=" * 60)
    if all_ok:
        print("🎉 环境检查完成，系统可以正常运行!")
        print("\n下一步可以运行:")
        print("python main.py --action status")
        print("python test_data_collection.py")
    else:
        print("⚠️  环境检查发现问题，请先解决上述问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
