# 格力电器历史财务数据收集系统

这是一个用于获取格力电器(000651.SZ)历史财务数据并存储到MySQL数据库的完整系统。

## 功能特性

- 📈 **日线行情数据**: 获取历史股价、成交量等交易数据
- 📊 **财务报表数据**: 获取利润表、资产负债表、现金流量表
- 🗄️ **数据库存储**: 自动创建表结构并存储数据
- 🔄 **增量更新**: 支持增量更新和去重
- ✅ **数据验证**: 内置数据完整性验证
- 📝 **日志记录**: 详细的操作日志

## 系统架构

```
├── config.py              # 配置文件(Tushare Token, 数据库配置)
├── models.py              # 数据库模型定义
├── data_fetcher.py        # Tushare数据获取器
├── data_storage.py        # 数据存储管理器
├── main.py               # 主执行脚本
├── test_data_collection.py # 测试和验证脚本
├── requirements.txt       # 依赖包列表
└── README.md             # 说明文档
```

## 数据库表结构

### 1. daily_stock_data (日线数据表)
- 股票代码、交易日期、开高低收价格
- 成交量、成交额、涨跌幅等

### 2. financial_statements (利润表)
- 营业收入、净利润、各项费用
- 经营利润、利润总额等

### 3. balance_sheet (资产负债表)
- 资产、负债、所有者权益
- 流动资产、固定资产等详细科目

### 4. cash_flow_statement (现金流量表)
- 经营活动现金流量
- 投资活动现金流量
- 筹资活动现金流量

## 安装和配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
确保MySQL数据库已安装并运行，然后在 `config.py` 中配置数据库连接信息：

```python
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_password',
    'database': 'qtdb'
}
```

### 3. 配置Tushare Token
在 `config.py` 中设置你的Tushare Token：

```python
TUSHARE_TOKEN = "your_tushare_token_here"
```

## 使用方法

### 1. 收集所有历史数据
```bash
python main.py --action collect
```

### 2. 收集指定时间范围的数据
```bash
python main.py --action collect --start-date 20200101 --end-date 20231231
```

### 3. 只收集特定类型的数据
```bash
python main.py --action collect --types daily income
```

### 4. 更新最近30天的数据
```bash
python main.py --action update --days 30
```

### 5. 查看当前数据状态
```bash
python main.py --action status
```

## 命令行参数

- `--action`: 操作类型 (collect/update/status)
- `--start-date`: 开始日期 (YYYYMMDD格式)
- `--end-date`: 结束日期 (YYYYMMDD格式)
- `--days`: 更新天数 (用于update操作)
- `--types`: 数据类型 (daily/income/balance/cashflow)

## 测试和验证

### 运行测试套件
```bash
python test_data_collection.py
```

测试包括：
- Tushare API连接测试
- 数据获取功能测试
- 数据存储功能测试
- 数据完整性验证
- 错误处理测试

## 数据获取说明

### 数据来源
- **数据提供商**: Tushare Pro
- **股票代码**: 000651.SZ (格力电器)
- **数据频率**: 
  - 日线数据: 每个交易日
  - 财务数据: 季报/年报

### 默认获取范围
- **日线数据**: 最近5年
- **财务数据**: 最近10年

### API限制
- Tushare有API调用频率限制
- 系统已内置延时机制避免超限

## 日志和监控

### 日志文件
- 运行日志保存在 `gree_data_collection.log`
- 包含详细的操作记录和错误信息

### 数据统计
系统会自动统计：
- 各类数据的记录数量
- 最新数据的日期
- 数据收集的增量情况

## 故障排除

### 常见问题

1. **Tushare Token错误**
   - 检查Token是否正确设置
   - 确认Token是否有效且有权限

2. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接配置是否正确

3. **数据获取为空**
   - 检查日期范围是否合理
   - 确认股票代码是否正确

4. **API调用超限**
   - 等待一段时间后重试
   - 检查Tushare账户权限

### 错误日志
查看 `gree_data_collection.log` 文件获取详细错误信息。

## 扩展功能

### 添加其他股票
修改 `config.py` 中的 `GREE_STOCK_CODE` 即可收集其他股票数据。

### 添加更多数据类型
可以扩展 `data_fetcher.py` 添加更多Tushare数据接口。

### 自定义数据处理
可以修改 `data_storage.py` 添加自定义的数据清洗和处理逻辑。

## 注意事项

1. **数据准确性**: 数据来源于Tushare，请以官方披露为准
2. **使用限制**: 仅供学习和研究使用
3. **API配额**: 注意Tushare API的调用限制
4. **数据更新**: 财务数据通常有延迟，建议定期更新

## 技术支持

如有问题，请检查：
1. 依赖包是否正确安装
2. 配置文件是否正确设置
3. 数据库是否正常运行
4. 网络连接是否正常

## 版本信息

- 版本: 1.0.0
- Python版本要求: 3.7+
- 数据库: MySQL 5.7+
