# 格力电器历史财务数据获取主程序
import argparse
import logging
from datetime import datetime, timedelta
import sys
import traceback

from data_fetcher import TushareDataFetcher
from data_storage import DataStorage
from models import create_tables
import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gree_data_collection.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class GreeDataCollector:
    """格力电器数据收集器"""
    
    def __init__(self):
        """初始化数据收集器"""
        self.fetcher = TushareDataFetcher()
        self.storage = DataStorage()
        
    def collect_all_data(self, start_date=None, end_date=None, data_types=None):
        """
        收集所有历史数据
        
        Args:
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            data_types (list): 要收集的数据类型列表，默认收集所有类型
            
        Returns:
            dict: 收集结果统计
        """
        logger.info("=" * 60)
        logger.info("开始收集格力电器历史财务数据")
        logger.info(f"股票代码: {config.GREE_STOCK_CODE}")
        logger.info(f"开始日期: {start_date or '默认(最近5-10年)'}")
        logger.info(f"结束日期: {end_date or '今天'}")
        logger.info("=" * 60)
        
        # 默认收集所有类型的数据
        if data_types is None:
            data_types = ['daily', 'income', 'balance', 'cashflow']
        
        results = {}
        total_start_time = datetime.now()
        
        try:
            # 1. 创建数据库表
            logger.info("1. 检查并创建数据库表...")
            create_tables()
            
            # 2. 获取股票基本信息
            logger.info("2. 获取股票基本信息...")
            basic_info = self.fetcher.get_basic_info()
            if not basic_info.empty:
                info = basic_info.iloc[0]
                logger.info(f"   股票名称: {info['name']}")
                logger.info(f"   所属行业: {info['industry']}")
                logger.info(f"   上市日期: {info['list_date']}")
            
            # 3. 获取数据前的统计
            logger.info("3. 获取数据收集前的统计...")
            before_summary = self.storage.get_data_summary()
            logger.info(f"   收集前统计: {before_summary}")
            
            # 4. 收集各类数据
            logger.info("4. 开始收集历史数据...")
            
            collected_data = {}
            
            if 'daily' in data_types:
                logger.info("   4.1 收集日线数据...")
                start_time = datetime.now()
                collected_data['daily'] = self.fetcher.get_daily_data(start_date, end_date)
                end_time = datetime.now()
                logger.info(f"   日线数据收集完成，耗时: {end_time - start_time}")
            
            if 'income' in data_types:
                logger.info("   4.2 收集利润表数据...")
                start_time = datetime.now()
                collected_data['income'] = self.fetcher.get_income_statement(start_date, end_date)
                end_time = datetime.now()
                logger.info(f"   利润表数据收集完成，耗时: {end_time - start_time}")
            
            if 'balance' in data_types:
                logger.info("   4.3 收集资产负债表数据...")
                start_time = datetime.now()
                collected_data['balance'] = self.fetcher.get_balance_sheet(start_date, end_date)
                end_time = datetime.now()
                logger.info(f"   资产负债表数据收集完成，耗时: {end_time - start_time}")
            
            if 'cashflow' in data_types:
                logger.info("   4.4 收集现金流量表数据...")
                start_time = datetime.now()
                collected_data['cashflow'] = self.fetcher.get_cash_flow_statement(start_date, end_date)
                end_time = datetime.now()
                logger.info(f"   现金流量表数据收集完成，耗时: {end_time - start_time}")
            
            # 5. 保存数据到数据库
            logger.info("5. 保存数据到数据库...")
            save_start_time = datetime.now()
            save_results = self.storage.save_all_data(collected_data)
            save_end_time = datetime.now()
            logger.info(f"   数据保存完成，耗时: {save_end_time - save_start_time}")
            
            # 6. 获取数据后的统计
            logger.info("6. 获取数据收集后的统计...")
            after_summary = self.storage.get_data_summary()
            logger.info(f"   收集后统计: {after_summary}")
            
            # 7. 计算增量统计
            logger.info("7. 数据收集增量统计:")
            for key in ['daily_count', 'income_count', 'balance_count', 'cashflow_count']:
                before_count = before_summary.get(key, 0)
                after_count = after_summary.get(key, 0)
                increment = after_count - before_count
                logger.info(f"   {key}: {before_count} -> {after_count} (+{increment})")
            
            # 8. 总结
            total_end_time = datetime.now()
            total_duration = total_end_time - total_start_time
            
            results = {
                'success': True,
                'total_duration': total_duration,
                'save_results': save_results,
                'before_summary': before_summary,
                'after_summary': after_summary,
                'collected_data_info': {
                    key: len(df) if not df.empty else 0 
                    for key, df in collected_data.items()
                }
            }
            
            logger.info("=" * 60)
            logger.info("数据收集完成!")
            logger.info(f"总耗时: {total_duration}")
            logger.info(f"收集的数据量: {results['collected_data_info']}")
            logger.info(f"保存结果: {save_results}")
            logger.info("=" * 60)
            
            return results
            
        except Exception as e:
            logger.error(f"数据收集过程中发生错误: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            
            results = {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            
            return results
    
    def update_recent_data(self, days=30):
        """
        更新最近的数据
        
        Args:
            days (int): 更新最近多少天的数据
            
        Returns:
            dict: 更新结果
        """
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
        
        logger.info(f"更新最近{days}天的数据...")
        return self.collect_all_data(start_date, end_date)
    
    def get_status(self):
        """
        获取当前数据状态
        
        Returns:
            dict: 数据状态信息
        """
        logger.info("获取当前数据状态...")
        summary = self.storage.get_data_summary()
        
        status = {
            'database_summary': summary,
            'stock_code': config.GREE_STOCK_CODE,
            'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        logger.info("当前数据状态:")
        for key, value in summary.items():
            logger.info(f"  {key}: {value}")
        
        return status

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='格力电器历史财务数据收集工具')
    parser.add_argument('--action', choices=['collect', 'update', 'status'], 
                       default='collect', help='执行的操作')
    parser.add_argument('--start-date', help='开始日期 (YYYYMMDD)')
    parser.add_argument('--end-date', help='结束日期 (YYYYMMDD)')
    parser.add_argument('--days', type=int, default=30, 
                       help='更新操作时的天数 (默认30天)')
    parser.add_argument('--types', nargs='+', 
                       choices=['daily', 'income', 'balance', 'cashflow'],
                       help='要收集的数据类型')
    
    args = parser.parse_args()
    
    collector = GreeDataCollector()
    
    try:
        if args.action == 'collect':
            # 收集历史数据
            results = collector.collect_all_data(
                start_date=args.start_date,
                end_date=args.end_date,
                data_types=args.types
            )
            
            if results['success']:
                print("\n✅ 数据收集成功完成!")
                print(f"总耗时: {results['total_duration']}")
            else:
                print("\n❌ 数据收集失败!")
                print(f"错误: {results['error']}")
                sys.exit(1)
                
        elif args.action == 'update':
            # 更新最近数据
            results = collector.update_recent_data(args.days)
            
            if results['success']:
                print(f"\n✅ 最近{args.days}天数据更新完成!")
            else:
                print(f"\n❌ 数据更新失败!")
                print(f"错误: {results['error']}")
                sys.exit(1)
                
        elif args.action == 'status':
            # 查看状态
            status = collector.get_status()
            print("\n📊 当前数据状态:")
            for key, value in status['database_summary'].items():
                print(f"  {key}: {value}")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        print("\n⚠️  操作被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行错误: {e}")
        print(f"\n❌ 程序执行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
