# 快速测试脚本
print("开始快速测试...")

try:
    # 测试导入
    print("1. 测试模块导入...")
    import config
    from data_fetcher import TushareDataFetcher
    from data_storage import DataStorage
    from models import create_tables
    print("✅ 模块导入成功")
    
    # 测试配置
    print("2. 测试配置...")
    print(f"   股票代码: {config.GREE_STOCK_CODE}")
    print(f"   数据库: {config.DB_CONFIG['database']}")
    print("✅ 配置读取成功")
    
    # 测试数据库表创建
    print("3. 测试数据库表创建...")
    create_tables()
    print("✅ 数据库表创建成功")
    
    # 测试数据获取器
    print("4. 测试数据获取器...")
    fetcher = TushareDataFetcher()
    basic_info = fetcher.get_basic_info()
    if not basic_info.empty:
        print(f"✅ 获取到股票信息: {basic_info.iloc[0]['name']}")
    else:
        print("⚠️  未获取到股票信息")
    
    # 测试数据存储器
    print("5. 测试数据存储器...")
    storage = DataStorage()
    summary = storage.get_data_summary()
    print(f"✅ 数据库统计: {summary}")
    
    print("\n🎉 快速测试完成，系统运行正常!")
    
except Exception as e:
    print(f"\n❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
